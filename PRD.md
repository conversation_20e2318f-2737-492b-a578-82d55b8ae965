# Annet Assistant 运维助手系统 - 产品需求文档（PRD）

## 版本信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **项目代号**: annet_assistant

---

## 1. 项目概述

### 1.1 项目名称
**Annet Assistant 运维助手系统**

### 1.2 项目目标
构建一个集成化的运维管理平台，为医疗信息化运维人员提供统一的管理工具，包括用户管理、版本控制、云影像服务、会诊管理等核心功能。

### 1.3 核心价值主张
- **一站式运维管理**: 集成多个运维工具于一个平台
- **自动化处理**: 自动生成二维码、链接等，提高工作效率
- **安全可靠**: 完善的用户认证和权限管理
- **易于使用**: 直观的Web界面，降低使用门槛

### 1.4 目标用户群体
- **主要用户**: 医疗信息化运维工程师
- **次要用户**: 系统管理员、技术支持人员
- **使用场景**: 
  - 日常运维管理
  - 用户信息查询
  - 版本发布管理
  - 云影像服务配置
  - 会诊系统维护

### 1.5 项目架构概览
```
前端层 (Web UI)
    ↓
业务逻辑层 (Flask Application)
    ↓
数据访问层 (MySQL Database)
    ↓
外部服务集成层 (Third-party APIs)
```

---

## 2. 功能需求

### 2.1 用户认证模块

#### 2.1.1 功能描述
提供安全的用户登录、登出和会话管理功能。

#### 2.1.2 具体功能
- **用户登录**: 
  - 用户名/密码验证
  - 登录记录追踪（IP、城市、运营商、时间）
  - 登录失败处理
- **会话管理**: 
  - Flask-Login集成
  - 自动会话过期
- **用户登出**: 安全的会话清理

#### 2.1.3 用户交互流程
1. 用户访问系统 → 重定向到登录页面
2. 输入用户名和密码 → 系统验证
3. 验证成功 → 记录登录信息 → 跳转到首页
4. 验证失败 → 显示错误信息

#### 2.1.4 界面要求
- 简洁的登录表单
- 错误信息提示
- 响应式设计

#### 2.1.5 优先级
**P0 - 核心功能**

### 2.2 数据查询模块

#### 2.2.1 功能描述
提供用户信息和关联数据的查询功能。

#### 2.2.2 具体功能
- **用户信息查询**:
  - 根据手机号查询用户基础信息
  - 显示用户角色、密码修改时间、失败次数等
- **关联信息查询**:
  - 查询用户数据账户信息
  - 显示组织架构、部门信息等

#### 2.2.3 用户交互流程
1. 选择查询类型
2. 输入查询条件（手机号）
3. 系统执行查询
4. 展示查询结果

#### 2.2.4 优先级
**P0 - 核心功能**

### 2.3 版本管理模块

#### 2.3.1 功能描述
管理移动应用版本，自动生成下载二维码。

#### 2.3.2 具体功能
- **版本列表展示**:
  - 显示所有版本信息
  - 下载统计（总数、iOS、Android）
- **二维码生成**:
  - 自动获取应用Logo和二维码
  - 图片合成和美化
  - 缓存机制避免重复生成

#### 2.3.3 技术实现
- Selenium自动化获取版本信息
- PIL图像处理和合成
- 文件系统缓存

#### 2.3.4 优先级
**P1 - 重要功能**

### 2.4 云影像模块

#### 2.4.1 功能描述
为不同医院生成云影像查看链接。

#### 2.4.2 具体功能
- **省医云影像**:
  - MD5签名验证
  - 链接生成和验证
- **云肿云影像**:
  - 时间戳签名
  - 参数加密
- **中肿云影像**:
  - RSA加密签名
  - Base64编码

#### 2.4.3 安全要求
- 所有链接必须包含有效签名
- 支持时间戳防重放攻击
- 敏感信息加密传输

#### 2.4.4 优先级
**P1 - 重要功能**

### 2.5 会诊管理模块

#### 2.5.1 功能描述
管理会诊记录和腾讯云群组。

#### 2.5.2 具体功能
- **会诊记录删除**:
  - 批量删除历史会诊记录
  - 保留最近10条记录
- **群组管理**:
  - 自动解散过期群组
  - 腾讯云IM集成

#### 2.5.3 优先级
**P2 - 辅助功能**

### 2.6 工具箱模块

#### 2.6.1 功能描述
提供各种实用工具。

#### 2.6.2 具体功能
- **字符串处理**:
  - Base64编解码
  - URL编解码
  - MD5加密
  - JSON格式化
- **时间工具**:
  - 时间戳转换
- **翻译工具**:
  - 有道翻译API集成

#### 2.6.3 优先级
**P2 - 辅助功能**

---

## 3. 技术规范

### 3.1 技术栈选择

#### 3.1.1 后端技术栈
- **框架**: Flask 2.3.2
- **数据库**: MySQL 5.7+
- **ORM**: PyMySQL 1.1.0
- **认证**: Flask-Login 0.6.2
- **表单**: Flask-WTF 1.1.1, WTForms 3.0.1

#### 3.1.2 前端技术栈
- **模板引擎**: Jinja2 3.1.2
- **CSS框架**: Bootstrap 5.x
- **JavaScript**: 原生JavaScript + jQuery

#### 3.1.3 第三方库
- **图像处理**: Pillow 10.0.0
- **Web自动化**: Selenium 4.11.2
- **加密**: pycryptodome 3.18.0
- **数据处理**: pandas 2.0.3
- **HTTP请求**: requests 2.31.0

#### 3.1.4 版本要求
- **Python**: 3.8+
- **MySQL**: 5.7+
- **Chrome**: 最新版本（Selenium需要）

### 3.2 项目目录结构

```
annet_assistant/
├── app.py                      # 主应用文件
├── requirements.txt            # 依赖包列表
├── module/                     # 业务模块
│   ├── __init__.py
│   ├── do_mysql_connect.py     # 数据库连接
│   ├── do_version_manage.py    # 版本管理
│   ├── do_imagecloud_link.py   # 云影像链接
│   ├── do_del_consultation.py  # 会诊管理
│   └── do_tools.py             # 工具集
├── templates/                  # HTML模板
│   ├── header.html             # 页面头部
│   ├── footer.html             # 页面底部
│   ├── index.html              # 首页
│   ├── login.html              # 登录页
│   ├── master_query_form.html  # 查询表单
│   ├── master_query_result.html # 查询结果
│   ├── app_versions.html       # 版本管理
│   ├── tools_image_link.html   # 云影像工具
│   ├── tools_handle_str.html   # 字符串工具


│   ├── tools_dict_info.html    # 字典信息
│   ├── GetSyImageLink.html     # 省医云影像
│   ├── add_dataaccount.html    # 添加数据账户
│   └── product_reg.html        # 产品注册
├── static/                     # 静态资源
│   ├── css/                    # 样式文件
│   ├── js/                     # JavaScript文件
│   ├── images/                 # 图片资源
│   ├── bootstrap/              # Bootstrap框架
│   ├── app/                    # 应用相关资源
│   │   ├── base_imgs/          # 基础图片
│   │   └── version_imgs/       # 版本二维码
│   ├── excel/                  # Excel文件
│   │   └── login_record.xlsx   # 登录记录
│   ├── txt/                    # 文本文件
│   │   └── dict_info.txt       # 字典信息文件
│   └── pdf/                    # PDF文件存储
└── venv/                       # 虚拟环境
```

### 3.3 代码规范

#### 3.3.1 Python代码规范
- 遵循PEP 8编码规范
- 使用4个空格缩进
- 函数和类使用docstring注释
- 变量命名使用下划线分隔
- 常量使用大写字母

#### 3.3.2 HTML/CSS规范
- 使用语义化HTML标签
- CSS类名使用短横线分隔
- 响应式设计，支持移动端

#### 3.3.3 JavaScript规范
- 使用ES6+语法
- 变量命名使用驼峰命名法
- 添加必要的注释

### 3.4 开发标准

#### 3.4.1 安全标准
- 所有用户输入必须验证和过滤
- 数据库查询使用参数化查询
- 敏感信息加密存储
- 实施CSRF保护

#### 3.4.2 性能标准
- 数据库连接使用连接池
- 静态资源启用缓存
- 图片生成结果缓存
- 接口响应时间 < 2秒

#### 3.4.3 可维护性标准
- 模块化设计，职责分离
- 统一的错误处理机制
- 完善的日志记录
- 代码注释覆盖率 > 80%

---

## 4. API接口文档

### 4.1 认证相关接口

#### 4.1.1 用户登录
- **接口路径**: `/login`
- **请求方法**: POST
- **请求参数**:
  ```json
  {
    "username": "string",  // 用户名
    "password": "string"   // 密码
  }
  ```
- **响应格式**:
  ```json
  {
    "status": "success|error",
    "message": "string",
    "redirect_url": "string"
  }
  ```
- **状态码**:
  - 200: 登录成功
  - 401: 认证失败
  - 400: 参数错误

#### 4.1.2 用户登出
- **接口路径**: `/logout`
- **请求方法**: GET
- **认证要求**: 需要登录
- **响应**: 重定向到登录页面

### 4.2 数据查询接口

#### 4.2.1 查询用户信息
- **接口路径**: `/get_user_info`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "phone_info": "string"  // 手机号
  }
  ```
- **响应格式**: HTML页面（查询结果）

#### 4.2.2 查询关联信息
- **接口路径**: `/get_user_bind_info`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "phone_band": "string"  // 手机号或数据账户
  }
  ```
- **响应格式**: HTML页面（查询结果）

### 4.3 版本管理接口

#### 4.3.1 版本列表
- **接口路径**: `/app_versions`
- **请求方法**: GET
- **认证要求**: 需要登录
- **响应格式**: HTML页面（版本列表）

### 4.4 云影像接口

#### 4.4.1 生成云影像链接
- **接口路径**: `/tools_image_link`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "hospital": "string",    // 医院类型: sy|yz|zz
    "patcard": "string",     // 患者卡号
    "patname": "string"      // 患者姓名（中肿需要）
  }
  ```
- **响应格式**: HTML页面（包含生成的链接）

#### 4.4.2 省医云影像链接
- **接口路径**: `/GetSyImageLink`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "patcard": "string",     // 患者编号
    "pwd": "string"          // 查询密码
  }
  ```
- **响应格式**: HTML页面（包含生成的链接）

### 4.5 会诊管理接口

#### 4.5.1 删除会诊记录
- **接口路径**: `/del_consultation_record`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "conn_user_id": "string"  // 用户ID
  }
  ```
- **响应格式**:
  ```json
  {
    "status": "success",
    "message": "会诊记录删除成功！"
  }
  ```

### 4.6 工具接口

#### 4.6.1 字符串处理
- **接口路径**: `/tools_handle_str`
- **请求方法**: POST
- **认证要求**: 需要登录
- **请求参数**:
  ```json
  {
    "handle_type": "string",  // 处理类型
    "input_str": "string"     // 输入字符串
  }
  ```
- **响应格式**: HTML页面（处理结果）

#### 4.6.2 字典信息查询
- **接口路径**: `/tools_dict_info`
- **请求方法**: GET
- **认证要求**: 需要登录
- **响应格式**: HTML页面（字典信息）

### 4.7 错误处理

#### 4.7.1 通用错误码
- **401**: 未授权访问，需要登录
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

#### 4.7.2 错误响应格式
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

---

## 5. 数据模型

### 5.1 数据库设计

#### 5.1.1 数据库配置
- **数据库类型**: MySQL 5.7+
- **字符集**: UTF-8
- **连接池**: 支持连接复用
- **备份策略**: 每日自动备份

#### 5.1.2 连接配置
```python
{
    "host": "annet-mysql1.mysql.rds.aliyuncs.com",
    "port": 3306,
    "user": "master_annet",
    "password": "!@#Annet123",
    "database": "annet_master20171116",
    "charset": "utf8"
}
```

### 5.2 数据表结构

#### 5.2.1 用户基础信息表 (user_baseinfo)
```sql
CREATE TABLE user_baseinfo (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    role VARCHAR(50) COMMENT '用户角色',
    userid VARCHAR(50) UNIQUE NOT NULL COMMENT '用户ID',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_userid (userid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户基础信息表';
```

#### 5.2.2 用户表 (user)
```sql
CREATE TABLE user (
    id INT PRIMARY KEY AUTO_INCREMENT,
    userid VARCHAR(50) NOT NULL COMMENT '用户ID',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    modify_password_time BIGINT COMMENT '密码修改时间戳',
    fail_num INT DEFAULT 0 COMMENT '登录失败次数',
    status TINYINT DEFAULT 1 COMMENT '用户状态 1:正常 0:禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (userid) REFERENCES user_baseinfo(userid),
    INDEX idx_userid (userid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';
```

#### 5.2.3 用户数据账户表 (user_dataaccount)
```sql
CREATE TABLE user_dataaccount (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dataaccount VARCHAR(100) NOT NULL COMMENT '数据账户',
    name VARCHAR(100) COMMENT '姓名',
    orgname VARCHAR(200) COMMENT '组织名称',
    deptname VARCHAR(200) COMMENT '部门名称',
    office VARCHAR(200) COMMENT '科室',
    userid VARCHAR(50) COMMENT '用户ID',
    phone VARCHAR(20) COMMENT '手机号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_dataaccount (dataaccount),
    INDEX idx_phone (phone),
    INDEX idx_userid (userid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户数据账户表';
```

### 5.3 数据流转逻辑

#### 5.3.1 用户认证流程
1. 用户提交登录信息
2. 系统查询user_baseinfo表验证用户存在性
3. 查询user表验证密码
4. 更新登录记录到Excel文件
5. 创建用户会话

#### 5.3.2 数据查询流程
1. 接收查询参数（手机号）
2. 执行SQL查询（JOIN多表）
3. 格式化查询结果
4. 返回HTML页面展示

#### 5.3.3 数据存储策略
- **用户数据**: 存储在MySQL数据库
- **登录记录**: 存储在Excel文件
- **生成的图片**: 存储在文件系统
- **配置信息**: 硬编码在代码中

### 5.4 数据验证规则

#### 5.4.1 输入验证
- **手机号**: 11位数字，符合中国手机号格式
- **用户名**: 非空，长度3-50字符
- **密码**: 非空，最小长度6位

#### 5.4.2 数据完整性
- 用户ID在系统中唯一
- 手机号在系统中唯一
- 外键约束确保数据一致性

#### 5.4.3 安全规则
- 密码使用哈希存储
- SQL查询使用参数化防注入
- 敏感信息不记录日志

---

## 6. 部署和配置

### 6.1 环境配置要求

#### 6.1.1 服务器环境
- **操作系统**: Linux (Ubuntu 18.04+ / CentOS 7+)
- **Python版本**: 3.8+
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低20GB，推荐50GB+
- **网络**: 支持HTTPS，开放9900端口

#### 6.1.2 依赖服务
- **MySQL数据库**: 5.7+
- **Chrome浏览器**: 最新版本
- **ChromeDriver**: 与Chrome版本匹配

#### 6.1.3 Python环境
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 6.2 部署流程

#### 6.2.1 代码部署
```bash
# 1. 克隆代码
git clone <repository_url> annet_assistant
cd annet_assistant

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 创建必要目录
mkdir -p static/app/base_imgs
mkdir -p static/app/version_imgs
mkdir -p static/pdf
mkdir -p static/excel

# 5. 设置权限
chmod +x app.py
chown -R www-data:www-data .
```

#### 6.2.2 数据库配置
```sql
-- 1. 创建数据库
CREATE DATABASE annet_master20171116 DEFAULT CHARSET=utf8;

-- 2. 创建用户
CREATE USER 'master_annet'@'%' IDENTIFIED BY '!@#Annet123';
GRANT ALL PRIVILEGES ON annet_master20171116.* TO 'master_annet'@'%';
FLUSH PRIVILEGES;

-- 3. 导入表结构
USE annet_master20171116;
-- 执行建表SQL语句
```

#### 6.2.3 配置文件设置
```python
# app.py 中的配置项
app.secret_key = 'your_secret_key_here'  # 修改为安全的密钥

# 数据库配置（在 do_mysql_connect.py 中）
DB_CONFIG = {
    'host': 'your_mysql_host',
    'port': 3306,
    'user': 'your_mysql_user',
    'password': 'your_mysql_password',
    'database': 'your_database_name',
    'charset': 'utf8'
}
```

### 6.3 启动配置

#### 6.3.1 开发环境启动
```bash
# 直接启动
python app.py

# 或使用Flask命令
export FLASK_APP=app.py
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=9900
```

#### 6.3.2 生产环境部署
```bash
# 使用Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:9900 app:app

# 使用systemd服务
sudo tee /etc/systemd/system/annet-assistant.service > /dev/null <<EOF
[Unit]
Description=Annet Assistant
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/annet_assistant
Environment=PATH=/path/to/annet_assistant/venv/bin
ExecStart=/path/to/annet_assistant/venv/bin/gunicorn -w 4 -b 0.0.0.0:9900 app:app
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable annet-assistant
sudo systemctl start annet-assistant
```

#### 6.3.3 Nginx反向代理
```nginx
server {
    listen 80;
    server_name your_domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:9900;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /path/to/annet_assistant/static;
        expires 30d;
    }
}
```

### 6.4 配置文件说明

#### 6.4.1 应用配置
```python
# 主要配置项
SECRET_KEY = 'your_secret_key'           # Flask密钥
DEBUG = False                            # 生产环境设为False
HOST = '0.0.0.0'                        # 监听地址
PORT = 9900                             # 监听端口
```

#### 6.4.2 数据库配置
```python
# MySQL连接配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'master_annet',
    'password': 'your_password',
    'database': 'annet_master20171116',
    'charset': 'utf8',
    'autocommit': True
}
```

#### 6.4.3 第三方服务配置
```python
# 腾讯云IM配置
TENCENT_SDKAPPID = '1400004073'
TENCENT_IDENTIFIER = 'ANNETPUSH'

# 版本管理配置
VERSION_API_URL = 'https://ci.annetinfo.com'
VERSION_USERNAME = 'admin'
VERSION_PASSWORD = 'annet_version_admin'
```

### 6.5 监控和维护

#### 6.5.1 日志配置
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日志
if not app.debug:
    file_handler = RotatingFileHandler('logs/annet_assistant.log', 
                                     maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

#### 6.5.2 健康检查
```python
@app.route('/health')
def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        MyConnectMySQL().quary_data("SELECT 1")
        return {'status': 'healthy', 'timestamp': time.time()}
    except Exception as e:
        return {'status': 'unhealthy', 'error': str(e)}, 500
```

#### 6.5.3 备份策略
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="annet_master20171116"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h localhost -u master_annet -p'!@#Annet123' $DB_NAME > $BACKUP_DIR/annet_${DATE}.sql

# 压缩备份文件
gzip $BACKUP_DIR/annet_${DATE}.sql

# 删除7天前的备份
find $BACKUP_DIR -name "annet_*.sql.gz" -mtime +7 -delete
```

---

## 7. 附录

### 7.1 开发规范补充

#### 7.1.1 Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

#### 7.1.2 代码审查清单
- [ ] 代码符合PEP 8规范
- [ ] 函数和类有完整的docstring
- [ ] 异常处理完善
- [ ] 安全性检查通过
- [ ] 性能测试通过
- [ ] 单元测试覆盖率 > 80%

### 7.2 常见问题解决

#### 7.2.1 部署问题
- **Chrome/ChromeDriver版本不匹配**: 确保版本一致
- **MySQL连接失败**: 检查网络和认证配置
- **静态文件404**: 检查Nginx配置和文件权限

#### 7.2.2 运行时问题
- **内存不足**: 增加服务器内存或优化代码
- **响应超时**: 检查数据库查询性能
- **图片生成失败**: 检查Selenium和Chrome配置

### 7.3 性能优化建议

#### 7.3.1 数据库优化
- 添加适当的索引
- 使用连接池
- 定期清理历史数据
- 查询语句优化

#### 7.3.2 应用优化
- 启用静态文件缓存
- 使用Redis缓存热点数据
- 异步处理耗时操作
- 图片压缩和CDN加速

---

**文档结束**

> 本PRD文档详细描述了Annet Assistant运维助手系统的完整需求和技术规范。开发团队可以基于此文档进行系统的完整重构和开发。如有疑问或需要补充，请及时沟通更新。
