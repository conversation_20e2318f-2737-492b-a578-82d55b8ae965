# 组件库规范 - <PERSON><PERSON> Assistant

## 设计理念
基于 Glassmorphism 设计风格，构建一套完整的组件库，确保界面的一致性和可复用性。所有组件都采用玻璃拟态效果，提供现代化的用户体验。

## 基础组件

### 1. 按钮组件 (Button)

#### 主要按钮
```css
.btn-primary {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(59, 130, 246, 0.6));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(37, 99, 235, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(59, 130, 246, 0.7));
}
```

#### 次要按钮
```css
.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  color: #1f2937;
  font-weight: 500;
  transition: all 0.3s ease;
}
```

#### 按钮尺寸
- **小按钮**: `padding: 8px 16px; font-size: 14px;`
- **标准按钮**: `padding: 12px 24px; font-size: 16px;`
- **大按钮**: `padding: 16px 32px; font-size: 18px;`

### 2. 输入框组件 (Input)

#### 文本输入框
```css
.form-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 16px;
  width: 100%;
  font-size: 16px;
  color: #1f2937;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(37, 99, 235, 0.4);
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}
```

#### 搜索输入框
```css
.search-input {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 25px;
  padding: 12px 20px 12px 45px;
  background-image: url('data:image/svg+xml;utf8,<svg>...</svg>');
  background-repeat: no-repeat;
  background-position: 15px center;
}
```

### 3. 卡片组件 (Card)

#### 基础卡片
```css
.card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.45);
}
```

#### 信息卡片
```css
.info-card {
  background: rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 24px;
}
```

#### 统计卡片
```css
.stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 18px;
  padding: 28px;
  text-align: center;
}
```

### 4. 导航组件 (Navigation)

#### 顶部导航
```css
.navbar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-link {
  color: #1f2937;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #2563eb;
}
```

#### 侧边导航
```css
.sidebar {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  width: 280px;
  height: 100vh;
  padding: 24px 0;
}

.sidebar-item {
  padding: 12px 24px;
  margin: 4px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-item.active {
  background: rgba(37, 99, 235, 0.2);
  color: #2563eb;
}
```

### 5. 模态框组件 (Modal)

#### 基础模态框
```css
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 40px;
  max-width: 600px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}
```

### 6. 表格组件 (Table)

#### 玻璃表格
```css
.glass-table {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  overflow: hidden;
}

.glass-table th {
  background: rgba(255, 255, 255, 0.2);
  padding: 16px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-table tr:hover {
  background: rgba(255, 255, 255, 0.1);
}
```

### 7. 表单组件 (Form)

#### 表单容器
```css
.form-container {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1f2937;
}
```

#### 选择框
```css
.form-select {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 16px;
  width: 100%;
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg>...</svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
}
```

### 8. 通知组件 (Alert)

#### 成功通知
```css
.alert-success {
  background: rgba(16, 185, 129, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  color: #065f46;
}
```

#### 错误通知
```css
.alert-error {
  background: rgba(239, 68, 68, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  color: #7f1d1d;
}
```

### 9. 加载组件 (Loading)

#### 玻璃加载器
```css
.glass-loader {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  border-top: 2px solid #2563eb;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 10. 工具提示组件 (Tooltip)

#### 基础工具提示
```css
.tooltip {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-size: 14px;
  position: absolute;
  z-index: 1500;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}
```

## 组件状态

### 悬停状态 (Hover)
- 轻微上移 `transform: translateY(-2px)`
- 增强阴影效果
- 提高背景透明度

### 激活状态 (Active)
- 轻微下压 `transform: translateY(1px)`
- 减少阴影效果
- 降低背景透明度

### 禁用状态 (Disabled)
- 降低透明度至 50%
- 移除交互效果
- 改变鼠标指针为 `not-allowed`

### 焦点状态 (Focus)
- 添加蓝色外发光效果
- 增强边框颜色
- 保持无障碍访问性

## 响应式设计

### 移动端适配
```css
@media (max-width: 768px) {
  .card { padding: 20px; border-radius: 16px; }
  .btn { padding: 10px 20px; font-size: 14px; }
  .form-input { padding: 10px 14px; }
  .modal { padding: 24px; border-radius: 20px; }
}
```

### 平板适配
```css
@media (min-width: 769px) and (max-width: 1024px) {
  .card { padding: 28px; }
  .sidebar { width: 240px; }
}
```

## 动画效果

### 进入动画
```css
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}
```

### 缩放动画
```css
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}
```
