# 字体系统 - <PERSON><PERSON> Assistant

## 设计理念
选择现代、清晰、易读的字体，确保在各种设备和屏幕上都能提供优秀的阅读体验。中英文字体搭配协调，体现专业性。

## 字体族 (Font Family)

### 中文字体
```css
font-family: 
  "PingFang SC",           /* macOS 系统字体 */
  "Hiragino Sans GB",      /* macOS 备选字体 */
  "Microsoft YaHei",       /* Windows 系统字体 */
  "WenQuanYi Micro Hei",   /* Linux 系统字体 */
  sans-serif;              /* 通用无衬线字体 */
```

### 英文字体
```css
font-family: 
  "Inter",                 /* 现代无衬线字体 */
  "SF Pro Display",        /* Apple 系统字体 */
  "Segoe UI",             /* Windows 系统字体 */
  "Roboto",               /* Android 系统字体 */
  sans-serif;             /* 通用无衬线字体 */
```

### 等宽字体 (代码/数据显示)
```css
font-family: 
  "SF Mono",              /* macOS 等宽字体 */
  "Monaco",               /* macOS 备选等宽字体 */
  "Consolas",             /* Windows 等宽字体 */
  "Liberation Mono",       /* Linux 等宽字体 */
  monospace;              /* 通用等宽字体 */
```

## 字体大小 (Font Size)

### 标题层级
- **H1 - 主标题**: `32px` (2rem) - 页面主标题
- **H2 - 二级标题**: `28px` (1.75rem) - 区块标题
- **H3 - 三级标题**: `24px` (1.5rem) - 卡片标题
- **H4 - 四级标题**: `20px` (1.25rem) - 小节标题
- **H5 - 五级标题**: `18px` (1.125rem) - 子标题
- **H6 - 六级标题**: `16px` (1rem) - 最小标题

### 正文层级
- **大号正文**: `18px` (1.125rem) - 重要内容
- **标准正文**: `16px` (1rem) - 常规内容
- **小号正文**: `14px` (0.875rem) - 辅助内容
- **极小正文**: `12px` (0.75rem) - 标签、提示

### 特殊用途
- **按钮文字**: `16px` (1rem) - 主要按钮
- **小按钮文字**: `14px` (0.875rem) - 次要按钮
- **表单标签**: `14px` (0.875rem) - 表单字段标签
- **输入框文字**: `16px` (1rem) - 输入框内容

## 字体粗细 (Font Weight)

### 粗细等级
- **Thin**: `100` - 极细，装饰用
- **Light**: `300` - 细体，辅助文本
- **Regular**: `400` - 常规，正文内容
- **Medium**: `500` - 中等，重要文本
- **SemiBold**: `600` - 半粗，小标题
- **Bold**: `700` - 粗体，标题
- **ExtraBold**: `800` - 极粗，强调

### 使用规范
- **标题**: 使用 SemiBold (600) 或 Bold (700)
- **正文**: 使用 Regular (400)
- **强调文本**: 使用 Medium (500) 或 SemiBold (600)
- **辅助文本**: 使用 Light (300) 或 Regular (400)

## 行高 (Line Height)

### 行高比例
- **标题行高**: `1.2` - 紧凑的标题间距
- **正文行高**: `1.6` - 舒适的阅读间距
- **小文本行高**: `1.4` - 适中的小文本间距
- **按钮行高**: `1.5` - 按钮内文字间距

### 具体数值
```css
/* 标题 */
h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
}

/* 正文 */
p, div, span {
  line-height: 1.6;
}

/* 小文本 */
.text-sm, .text-xs {
  line-height: 1.4;
}

/* 按钮 */
button, .btn {
  line-height: 1.5;
}
```

## 字间距 (Letter Spacing)

### 间距设置
- **标题**: `-0.025em` - 略微紧缩
- **正文**: `0` - 默认间距
- **小文本**: `0.025em` - 略微放宽
- **按钮**: `0.05em` - 清晰易读

## 响应式字体

### 移动端适配
```css
/* 移动端字体缩放 */
@media (max-width: 768px) {
  h1 { font-size: 28px; }
  h2 { font-size: 24px; }
  h3 { font-size: 20px; }
  h4 { font-size: 18px; }
  h5 { font-size: 16px; }
  h6 { font-size: 14px; }
  
  body { font-size: 14px; }
  .text-lg { font-size: 16px; }
  .text-sm { font-size: 12px; }
}
```

## 字体加载优化

### 字体预加载
```html
<link rel="preload" href="/fonts/Inter-Regular.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/fonts/Inter-SemiBold.woff2" as="font" type="font/woff2" crossorigin>
```

### 字体显示策略
```css
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-display: swap; /* 优化字体加载体验 */
  font-weight: 400;
  font-style: normal;
}
```

## 文本样式类

### 实用类定义
```css
/* 字体大小 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

/* 字体粗细 */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 文本颜色 */
.text-primary { color: #2563eb; }
.text-secondary { color: #4b5563; }
.text-muted { color: #9ca3af; }
.text-success { color: #10b981; }
.text-warning { color: #f59e0b; }
.text-error { color: #ef4444; }
```

## 无障碍访问

### 字体大小要求
- 最小字体大小不低于 12px
- 重要内容字体大小不低于 14px
- 支持用户字体大小调整

### 对比度要求
- 正文文本对比度至少 4.5:1
- 大文本对比度至少 3:1
- 装饰性文本可适当降低对比度
