# Glassmorphism 设计规范 - <PERSON><PERSON> Assistant

## 设计理念
Glassmorphism（玻璃拟态）是一种现代设计趋势，通过半透明背景、模糊效果和精致边框创造出类似磨砂玻璃的视觉效果。为 Anne<PERSON> Assistant 带来现代、专业且具有层次感的用户界面。

## 核心特征

### 1. 半透明背景 (Semi-transparent Background)
- 使用 `rgba()` 或 `hsla()` 颜色值
- 透明度通常在 0.1 - 0.4 之间
- 根据内容重要性调整透明度

### 2. 背景模糊 (Backdrop Blur)
- 使用 `backdrop-filter: blur()` 属性
- 模糊半径通常在 10px - 20px 之间
- 创造景深效果

### 3. 精致边框 (Subtle Border)
- 使用半透明的白色或彩色边框
- 边框宽度通常为 1px
- 增强玻璃质感

### 4. 柔和阴影 (Soft Shadow)
- 使用多层 `box-shadow`
- 阴影颜色透明度较低
- 创造浮动效果

## 基础玻璃样式

### 主要玻璃容器
```css
.glass-primary {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5);
}
```

### 次要玻璃容器
```css
.glass-secondary {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  box-shadow: 
    0 4px 16px 0 rgba(31, 38, 135, 0.25),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3);
}
```

### 深色玻璃容器
```css
.glass-dark {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 14px;
  box-shadow: 
    0 6px 24px 0 rgba(0, 0, 0, 0.3),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
}
```

## 组件级玻璃样式

### 玻璃卡片
```css
.glass-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px 0 rgba(31, 38, 135, 0.45),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5);
}
```

### 玻璃按钮
```css
.glass-btn {
  background: rgba(37, 99, 235, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(37, 99, 235, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  color: #2563eb;
  font-weight: 600;
  box-shadow: 
    0 4px 16px 0 rgba(37, 99, 235, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.glass-btn:hover {
  background: rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
  box-shadow: 
    0 6px 20px 0 rgba(37, 99, 235, 0.3),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.4);
}

.glass-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px 0 rgba(37, 99, 235, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
}
```

### 玻璃输入框
```css
.glass-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 16px;
  color: #1f2937;
  font-size: 16px;
  box-shadow: 
    inset 0 2px 4px 0 rgba(31, 38, 135, 0.1),
    0 2px 8px 0 rgba(31, 38, 135, 0.15);
  transition: all 0.3s ease;
}

.glass-input:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(37, 99, 235, 0.4);
  outline: none;
  box-shadow: 
    inset 0 2px 4px 0 rgba(31, 38, 135, 0.1),
    0 0 0 3px rgba(37, 99, 235, 0.1),
    0 4px 12px 0 rgba(37, 99, 235, 0.2);
}

.glass-input::placeholder {
  color: rgba(75, 85, 99, 0.7);
}
```

## 特殊效果

### 渐变玻璃
```css
.glass-gradient {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid;
  border-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  ) 1;
  border-radius: 16px;
}
```

### 彩色玻璃
```css
.glass-blue {
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 
    0 6px 24px 0 rgba(59, 130, 246, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3);
}

.glass-green {
  background: rgba(16, 185, 129, 0.15);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(16, 185, 129, 0.2);
  box-shadow: 
    0 6px 24px 0 rgba(16, 185, 129, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3);
}
```

## 动画效果

### 玻璃浮动动画
```css
@keyframes glass-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.glass-float {
  animation: glass-float 6s ease-in-out infinite;
}
```

### 玻璃脉动动画
```css
@keyframes glass-pulse {
  0%, 100% {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
  50% {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
  }
}

.glass-pulse {
  animation: glass-pulse 3s ease-in-out infinite;
}
```

## 响应式适配

### 移动端优化
```css
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 16px;
  }
  
  .glass-btn {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    padding: 10px 20px;
  }
}

/* 低性能设备回退 */
@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .glass-btn,
  .glass-input {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.9);
  }
}
```

## 浏览器兼容性

### 特性检测
```css
/* 支持 backdrop-filter 的浏览器 */
@supports (backdrop-filter: blur(10px)) {
  .glass-fallback {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
  }
}

/* 不支持的浏览器回退 */
@supports not (backdrop-filter: blur(10px)) {
  .glass-fallback {
    background: rgba(255, 255, 255, 0.85);
  }
}
```

## 性能优化

### 减少重绘
```css
.glass-optimized {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}
```

### 条件加载
```css
/* 仅在高性能设备上启用复杂效果 */
@media (min-resolution: 2dppx) and (min-width: 1024px) {
  .glass-premium {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }
}
```

## 使用指南

### 层级关系
1. **背景层**: 使用深色或渐变背景
2. **玻璃层**: 主要内容容器
3. **内容层**: 文字、图标等内容
4. **交互层**: 按钮、链接等可交互元素

### 最佳实践
- 避免过度使用模糊效果影响性能
- 确保文字在玻璃背景上的可读性
- 在移动设备上适当减少效果复杂度
- 提供无障碍访问的回退方案
