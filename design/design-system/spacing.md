# 间距系统 - <PERSON><PERSON> Assistant

## 设计理念
采用 8px 基础网格系统，确保界面元素的一致性和视觉节奏感。所有间距都基于 8 的倍数，便于开发实现和设计维护。

## 基础间距单位

### 间距比例 (8px 基础网格)
- **0**: `0px` - 无间距
- **1**: `4px` - 极小间距
- **2**: `8px` - 小间距
- **3**: `12px` - 小中间距
- **4**: `16px` - 标准间距
- **5**: `20px` - 中间距
- **6**: `24px` - 大间距
- **8**: `32px` - 很大间距
- **10**: `40px` - 极大间距
- **12**: `48px` - 超大间距
- **16**: `64px` - 巨大间距
- **20**: `80px` - 最大间距

## 内边距 (Padding)

### 组件内边距
```css
/* 按钮内边距 */
.btn-sm { padding: 8px 16px; }      /* 小按钮 */
.btn { padding: 12px 24px; }        /* 标准按钮 */
.btn-lg { padding: 16px 32px; }     /* 大按钮 */

/* 输入框内边距 */
.input-sm { padding: 8px 12px; }    /* 小输入框 */
.input { padding: 12px 16px; }      /* 标准输入框 */
.input-lg { padding: 16px 20px; }   /* 大输入框 */

/* 卡片内边距 */
.card-sm { padding: 16px; }         /* 小卡片 */
.card { padding: 24px; }            /* 标准卡片 */
.card-lg { padding: 32px; }         /* 大卡片 */
```

### 容器内边距
```css
/* 页面容器 */
.container { padding: 0 24px; }     /* 页面左右边距 */
.container-sm { padding: 0 16px; }  /* 小屏幕页面边距 */

/* 区块内边距 */
.section { padding: 48px 0; }       /* 区块上下间距 */
.section-sm { padding: 32px 0; }    /* 小区块间距 */
.section-lg { padding: 64px 0; }    /* 大区块间距 */
```

## 外边距 (Margin)

### 元素间距
```css
/* 标题间距 */
h1 { margin-bottom: 32px; }         /* 主标题下间距 */
h2 { margin-bottom: 24px; }         /* 二级标题下间距 */
h3 { margin-bottom: 20px; }         /* 三级标题下间距 */
h4, h5, h6 { margin-bottom: 16px; } /* 小标题下间距 */

/* 段落间距 */
p { margin-bottom: 16px; }          /* 段落下间距 */
.text-lg { margin-bottom: 20px; }   /* 大文本下间距 */
.text-sm { margin-bottom: 12px; }   /* 小文本下间距 */

/* 列表间距 */
ul, ol { margin-bottom: 16px; }     /* 列表下间距 */
li { margin-bottom: 8px; }          /* 列表项间距 */
```

### 组件间距
```css
/* 按钮组间距 */
.btn + .btn { margin-left: 12px; }  /* 按钮之间间距 */
.btn-group .btn { margin-right: 8px; } /* 按钮组内间距 */

/* 表单间距 */
.form-group { margin-bottom: 20px; } /* 表单组间距 */
.form-row { margin-bottom: 16px; }   /* 表单行间距 */

/* 卡片间距 */
.card + .card { margin-top: 24px; }  /* 卡片之间间距 */
.card-grid .card { margin-bottom: 24px; } /* 卡片网格间距 */
```

## 网格系统

### 栅格间距
```css
/* 栅格系统 */
.row { margin: 0 -12px; }           /* 行负边距 */
.col { padding: 0 12px; }           /* 列内边距 */

/* 响应式栅格间距 */
@media (max-width: 768px) {
  .row { margin: 0 -8px; }
  .col { padding: 0 8px; }
}
```

### 布局间距
```css
/* 主要布局区域 */
.header { padding: 16px 0; }        /* 头部内边距 */
.main { padding: 32px 0; }          /* 主内容区内边距 */
.footer { padding: 24px 0; }        /* 底部内边距 */

/* 侧边栏 */
.sidebar { padding: 24px 16px; }    /* 侧边栏内边距 */
.sidebar-item { margin-bottom: 16px; } /* 侧边栏项间距 */
```

## Glassmorphism 特殊间距

### 玻璃容器间距
```css
/* 玻璃卡片 */
.glass-card {
  padding: 32px;                    /* 内边距 */
  margin: 24px 0;                   /* 外边距 */
}

.glass-card-sm {
  padding: 20px;                    /* 小玻璃卡片内边距 */
  margin: 16px 0;                   /* 小玻璃卡片外边距 */
}

.glass-card-lg {
  padding: 48px;                    /* 大玻璃卡片内边距 */
  margin: 32px 0;                   /* 大玻璃卡片外边距 */
}
```

### 层叠效果间距
```css
/* 层叠玻璃效果 */
.glass-stack .glass-item:not(:last-child) {
  margin-bottom: -8px;              /* 层叠重叠间距 */
}

.glass-float {
  margin: 16px;                     /* 浮动玻璃元素间距 */
}
```

## 响应式间距

### 移动端适配
```css
/* 移动端间距调整 */
@media (max-width: 768px) {
  /* 减少大间距 */
  .section { padding: 32px 0; }
  .card { padding: 16px; }
  .container { padding: 0 16px; }
  
  /* 调整标题间距 */
  h1 { margin-bottom: 24px; }
  h2 { margin-bottom: 20px; }
  h3 { margin-bottom: 16px; }
  
  /* 调整按钮间距 */
  .btn { padding: 10px 20px; }
  .btn + .btn { margin-left: 8px; }
}

@media (max-width: 480px) {
  /* 进一步减少间距 */
  .section { padding: 24px 0; }
  .card { padding: 12px; }
  .container { padding: 0 12px; }
  
  /* 紧凑布局 */
  .form-group { margin-bottom: 16px; }
  .card + .card { margin-top: 16px; }
}
```

## 实用间距类

### Margin 类
```css
/* 外边距 */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }
.m-8 { margin: 32px; }

/* 方向性外边距 */
.mt-4 { margin-top: 16px; }
.mr-4 { margin-right: 16px; }
.mb-4 { margin-bottom: 16px; }
.ml-4 { margin-left: 16px; }
.mx-4 { margin-left: 16px; margin-right: 16px; }
.my-4 { margin-top: 16px; margin-bottom: 16px; }
```

### Padding 类
```css
/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }
.p-8 { padding: 32px; }

/* 方向性内边距 */
.pt-4 { padding-top: 16px; }
.pr-4 { padding-right: 16px; }
.pb-4 { padding-bottom: 16px; }
.pl-4 { padding-left: 16px; }
.px-4 { padding-left: 16px; padding-right: 16px; }
.py-4 { padding-top: 16px; padding-bottom: 16px; }
```

## 特殊场景间距

### 表格间距
```css
table { margin-bottom: 24px; }
th, td { padding: 12px 16px; }
th { padding-bottom: 16px; }
```

### 表单间距
```css
.form-label { margin-bottom: 8px; }
.form-input { margin-bottom: 16px; }
.form-help { margin-top: 4px; }
.form-error { margin-top: 4px; }
```

### 导航间距
```css
.nav-item { padding: 8px 16px; }
.nav-link { padding: 12px 16px; }
.breadcrumb-item { margin-right: 8px; }
```
