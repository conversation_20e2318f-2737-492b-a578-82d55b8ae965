# 色彩系统 - <PERSON><PERSON> Assistant

## 设计理念
基于医疗行业的专业性和现代化需求，采用蓝色系为主色调，配合中性色和功能色，营造专业、可信赖的视觉氛围。

## 主色调 (Primary Colors)

### 主蓝色 (Primary Blue)
- **主色**: `#2563eb` - 用于主要按钮、链接、重要信息
- **浅色**: `#3b82f6` - 用于悬停状态
- **深色**: `#1d4ed8` - 用于激活状态
- **极浅**: `#dbeafe` - 用于背景色

### 辅助蓝色 (Secondary Blue)
- **辅助色**: `#0ea5e9` - 用于次要按钮、信息提示
- **浅色**: `#38bdf8` - 用于悬停状态
- **深色**: `#0284c7` - 用于激活状态

## 中性色 (Neutral Colors)

### 灰色系
- **深灰**: `#1f2937` - 用于主要文本
- **中灰**: `#4b5563` - 用于次要文本
- **浅灰**: `#9ca3af` - 用于辅助文本
- **极浅灰**: `#f3f4f6` - 用于背景

### 白色系
- **纯白**: `#ffffff` - 用于卡片背景
- **暖白**: `#fefefe` - 用于页面背景
- **冷白**: `#f8fafc` - 用于输入框背景

## 功能色 (Functional Colors)

### 成功色 (Success)
- **主色**: `#10b981` - 成功状态
- **浅色**: `#34d399` - 成功悬停
- **背景**: `#d1fae5` - 成功背景

### 警告色 (Warning)
- **主色**: `#f59e0b` - 警告状态
- **浅色**: `#fbbf24` - 警告悬停
- **背景**: `#fef3c7` - 警告背景

### 错误色 (Error)
- **主色**: `#ef4444` - 错误状态
- **浅色**: `#f87171` - 错误悬停
- **背景**: `#fee2e2` - 错误背景

### 信息色 (Info)
- **主色**: `#3b82f6` - 信息状态
- **浅色**: `#60a5fa` - 信息悬停
- **背景**: `#dbeafe` - 信息背景

## Glassmorphism 专用色彩

### 玻璃效果背景
- **主背景**: `rgba(255, 255, 255, 0.25)` - 主要玻璃容器
- **次背景**: `rgba(255, 255, 255, 0.18)` - 次要玻璃容器
- **深色背景**: `rgba(30, 41, 59, 0.4)` - 深色玻璃容器

### 边框色
- **玻璃边框**: `rgba(255, 255, 255, 0.18)` - 玻璃容器边框
- **强调边框**: `rgba(37, 99, 235, 0.3)` - 强调边框

### 阴影色
- **轻阴影**: `rgba(0, 0, 0, 0.1)` - 轻微阴影
- **中阴影**: `rgba(0, 0, 0, 0.15)` - 中等阴影
- **重阴影**: `rgba(0, 0, 0, 0.25)` - 重阴影

## 渐变色 (Gradients)

### 背景渐变
```css
/* 主背景渐变 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 卡片背景渐变 */
background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

/* 按钮渐变 */
background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
```

## 色彩使用规范

### 文本色彩
- **标题**: 使用深灰色 `#1f2937`
- **正文**: 使用中灰色 `#4b5563`
- **辅助文本**: 使用浅灰色 `#9ca3af`
- **链接**: 使用主蓝色 `#2563eb`

### 背景色彩
- **页面背景**: 使用渐变背景
- **卡片背景**: 使用玻璃效果背景
- **输入框背景**: 使用冷白色 `#f8fafc`

### 交互色彩
- **悬停**: 颜色加深 10%
- **激活**: 颜色加深 20%
- **禁用**: 透明度降至 50%

## 无障碍访问
- 确保文本与背景的对比度至少为 4.5:1
- 重要信息不仅依赖颜色传达
- 支持高对比度模式
