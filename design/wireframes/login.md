# 登录页线框图设计 - Annet Assistant

## 页面概述
登录页采用全屏背景设计，中央放置玻璃拟态登录卡片，营造现代、专业的医疗信息化系统入口体验。

## 布局结构

### 整体布局
```
┌─────────────────────────────────────────────────────────────┐
│                    全屏渐变背景                              │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                  顶部区域                           │    │
│  │  ┌─────────────┐                    ┌─────────────┐ │    │
│  │  │    Logo     │                    │  语言切换   │ │    │
│  │  └─────────────┘                    └─────────────┘ │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│              ┌─────────────────────────────┐                │
│              │                             │                │
│              │        登录卡片             │                │
│              │     (玻璃拟态效果)          │                │
│              │                             │                │
│              │  ┌─────────────────────┐   │                │
│              │  │      系统标题       │   │                │
│              │  └─────────────────────┘   │                │
│              │                             │                │
│              │  ┌─────────────────────┐   │                │
│              │  │    用户名输入框     │   │                │
│              │  └─────────────────────┘   │                │
│              │                             │                │
│              │  ┌─────────────────────┐   │                │
│              │  │    密码输入框       │   │                │
│              │  └─────────────────────┘   │                │
│              │                             │                │
│              │  ┌─────────────────────┐   │                │
│              │  │      登录按钮       │   │                │
│              │  └─────────────────────┘   │                │
│              │                             │                │
│              │  ┌─────────────────────┐   │                │
│              │  │      错误提示       │   │                │
│              │  └─────────────────────┘   │                │
│              │                             │                │
│              └─────────────────────────────┘                │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                  底部区域                           │    │
│  │              版权信息 | 技术支持                    │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## 详细组件设计

### 1. 背景设计
- **背景类型**: 渐变背景 + 几何图形装饰
- **主渐变**: 从左上到右下的蓝紫色渐变
- **装饰元素**: 半透明的几何形状浮动效果
- **尺寸**: 全屏覆盖 (100vw × 100vh)

### 2. 顶部区域
- **Logo区域**:
  - 位置: 左上角
  - 内容: Annet Assistant Logo + 文字
  - 样式: 白色/半透明样式
  - 尺寸: 200px × 60px

- **语言切换**:
  - 位置: 右上角
  - 选项: 中文/English
  - 样式: 玻璃按钮效果
  - 尺寸: 120px × 40px

### 3. 登录卡片 (主要区域)
- **整体尺寸**: 420px × 580px
- **位置**: 屏幕居中
- **背景**: 玻璃拟态效果
- **圆角**: 24px
- **内边距**: 48px
- **阴影**: 深度阴影效果

#### 3.1 系统标题
- **主标题**: "Annet Assistant"
- **副标题**: "运维助手系统"
- **字体大小**: 主标题 28px, 副标题 16px
- **颜色**: 深灰色 (#1f2937)
- **对齐**: 居中
- **间距**: 标题下方 32px

#### 3.2 用户名输入框
- **标签**: "用户名"
- **占位符**: "请输入用户名"
- **尺寸**: 100% × 48px
- **样式**: 玻璃输入框效果
- **图标**: 用户图标 (左侧)
- **间距**: 下方 20px

#### 3.3 密码输入框
- **标签**: "密码"
- **占位符**: "请输入密码"
- **尺寸**: 100% × 48px
- **样式**: 玻璃输入框效果
- **图标**: 锁图标 (左侧) + 显示/隐藏图标 (右侧)
- **间距**: 下方 32px

#### 3.4 登录按钮
- **文字**: "登录"
- **尺寸**: 100% × 48px
- **样式**: 主要按钮 (蓝色渐变)
- **效果**: 悬停上升效果
- **间距**: 下方 24px

#### 3.5 错误提示区域
- **显示条件**: 登录失败时显示
- **样式**: 红色玻璃背景
- **图标**: 警告图标
- **动画**: 淡入效果
- **高度**: 自适应内容

### 4. 底部区域
- **版权信息**: "© 2025 Annet Assistant. All rights reserved."
- **技术支持**: "技术支持: Annet Info"
- **位置**: 页面底部居中
- **样式**: 半透明白色文字
- **字体大小**: 14px

## 交互设计

### 1. 输入框交互
- **焦点状态**: 边框发光效果
- **输入状态**: 实时验证
- **错误状态**: 红色边框 + 错误提示

### 2. 按钮交互
- **悬停效果**: 上升 + 阴影增强
- **点击效果**: 轻微下压
- **加载状态**: 旋转加载图标

### 3. 表单验证
- **实时验证**: 输入时验证格式
- **提交验证**: 提交时验证完整性
- **错误显示**: 字段下方显示具体错误

## 响应式设计

### 桌面端 (≥1024px)
- 登录卡片: 420px × 580px
- 背景装饰: 完整显示
- 字体大小: 标准尺寸

### 平板端 (768px - 1023px)
- 登录卡片: 380px × 540px
- 内边距: 40px
- 字体大小: 略微缩小

### 移动端 (≤767px)
- 登录卡片: 90% 宽度, 最大 360px
- 内边距: 32px
- 顶部Logo: 隐藏或缩小
- 字体大小: 移动端优化

## 动画效果

### 1. 页面加载动画
- 背景: 渐变淡入 (0.8s)
- 装饰元素: 浮动动画 (持续)
- 登录卡片: 从下方滑入 + 淡入 (0.6s)

### 2. 交互动画
- 输入框焦点: 边框发光 (0.3s)
- 按钮悬停: 上升效果 (0.3s)
- 错误提示: 淡入 + 轻微震动 (0.4s)

### 3. 成功登录动画
- 按钮: 成功图标替换文字
- 卡片: 淡出效果
- 页面: 切换到主页

## 无障碍设计

### 1. 键盘导航
- Tab 键顺序: 用户名 → 密码 → 登录按钮
- Enter 键: 提交表单
- Escape 键: 清除错误提示

### 2. 屏幕阅读器
- 表单标签: 正确关联
- 错误信息: aria-describedby 属性
- 按钮状态: aria-disabled 属性

### 3. 对比度
- 文字对比度: 至少 4.5:1
- 错误信息: 高对比度显示
- 焦点指示: 清晰可见

## 技术实现要点

### 1. CSS 关键特性
- backdrop-filter: 玻璃模糊效果
- CSS Grid/Flexbox: 布局实现
- CSS 变量: 主题色彩管理
- 媒体查询: 响应式适配

### 2. JavaScript 功能
- 表单验证: 实时 + 提交验证
- 动画控制: GSAP 或 CSS 动画
- 状态管理: 登录状态处理
- 错误处理: 友好的错误提示

### 3. 性能优化
- 图片预加载: 背景和图标
- CSS 压缩: 减少文件大小
- 懒加载: 非关键资源延迟加载
- 缓存策略: 静态资源缓存
