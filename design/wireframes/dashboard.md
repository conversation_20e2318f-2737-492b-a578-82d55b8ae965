# 仪表板页面线框图设计 - Annet Assistant

## 页面概述
仪表板作为系统主页，提供系统概览、快速访问功能模块、统计信息展示等核心功能。采用卡片式布局，每个功能模块都使用玻璃拟态效果。

## 布局结构

### 整体布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              顶部导航栏                                      │
│  ┌─────────────┐  ┌─────────────────────────────────┐  ┌─────────────────┐  │
│  │    Logo     │  │           导航菜单              │  │   用户信息      │  │
│  └─────────────┘  └─────────────────────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│                                主内容区                                      │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                          欢迎区域                                   │    │
│  │  ┌─────────────────┐                    ┌─────────────────────┐    │    │
│  │  │   欢迎信息      │                    │     系统状态        │    │    │
│  │  └─────────────────┘                    └─────────────────────┘    │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        统计信息区域                                  │    │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐   │    │
│  │  │ 用户总数│  │版本数量 │  │今日查询 │  │系统运行 │  │ 错误统计│   │    │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘  └─────────┘   │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        功能模块区域                                  │    │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │    │
│  │  │   数据查询      │  │   版本管理      │  │   云影像工具    │     │    │
│  │  │                 │  │                 │  │                 │     │    │
│  │  │   [图标]        │  │   [图标]        │  │   [图标]        │     │    │
│  │  │   描述信息      │  │   描述信息      │  │   描述信息      │     │    │
│  │  │   [进入按钮]    │  │   [进入按钮]    │  │   [进入按钮]    │     │    │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │    │
│  │                                                                     │    │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │    │
│  │  │   会诊管理      │  │   工具箱        │  │   系统设置      │     │    │
│  │  │                 │  │                 │  │                 │     │    │
│  │  │   [图标]        │  │   [图标]        │  │   [图标]        │     │    │
│  │  │   描述信息      │  │   描述信息      │  │   描述信息      │     │    │
│  │  │   [进入按钮]    │  │   [进入按钮]    │  │   [进入按钮]    │     │    │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        最近活动区域                                  │    │
│  │  ┌─────────────────────────────────────────────────────────────┐   │    │
│  │  │                      活动日志                               │   │    │
│  │  │  • 2025-01-15 14:30 - 用户查询: 138****1234               │   │    │
│  │  │  • 2025-01-15 14:25 - 版本发布: v2.1.0                   │   │    │
│  │  │  • 2025-01-15 14:20 - 云影像链接生成                     │   │    │
│  │  │  • 2025-01-15 14:15 - 会诊记录清理                       │   │    │
│  │  │  • 2025-01-15 14:10 - 系统备份完成                       │   │    │
│  │  └─────────────────────────────────────────────────────────────┘   │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 详细组件设计

### 1. 顶部导航栏
- **高度**: 80px
- **背景**: 玻璃拟态效果
- **位置**: 固定在顶部
- **阴影**: 轻微下阴影

#### 1.1 Logo区域
- **位置**: 左侧
- **内容**: Logo + "Annet Assistant"
- **尺寸**: 200px × 60px
- **样式**: 白色/深色文字

#### 1.2 导航菜单
- **位置**: 中央
- **菜单项**: 首页、数据查询、版本管理、云影像、会诊管理、工具箱
- **样式**: 水平排列，玻璃按钮效果
- **交互**: 悬停高亮，当前页面标记

#### 1.3 用户信息区域
- **位置**: 右侧
- **内容**: 用户头像 + 用户名 + 下拉菜单
- **下拉菜单**: 个人设置、修改密码、退出登录
- **尺寸**: 180px × 60px

### 2. 欢迎区域
- **高度**: 120px
- **背景**: 渐变玻璃效果
- **布局**: 左右分栏

#### 2.1 欢迎信息 (左侧)
- **主标题**: "欢迎回来，[用户名]"
- **副标题**: "今天是 2025年1月15日，星期三"
- **样式**: 大字体，深色文字

#### 2.2 系统状态 (右侧)
- **服务状态**: 正常运行
- **在线用户**: 128 人
- **系统负载**: 正常
- **样式**: 状态指示器 + 文字

### 3. 统计信息区域
- **布局**: 5列网格
- **间距**: 24px
- **高度**: 140px

#### 统计卡片设计
- **背景**: 玻璃拟态效果
- **圆角**: 16px
- **内边距**: 24px
- **内容结构**:
  - 图标 (顶部)
  - 数值 (中央，大字体)
  - 标题 (底部)
  - 趋势指示器 (可选)

#### 3.1 用户总数卡片
- **图标**: 用户群组图标
- **数值**: "1,234"
- **标题**: "注册用户"
- **趋势**: "+12 本月"

#### 3.2 版本数量卡片
- **图标**: 版本标签图标
- **数值**: "56"
- **标题**: "应用版本"
- **趋势**: "+3 本周"

#### 3.3 今日查询卡片
- **图标**: 搜索图标
- **数值**: "89"
- **标题**: "今日查询"
- **趋势**: "+15 较昨日"

#### 3.4 系统运行卡片
- **图标**: 服务器图标
- **数值**: "99.9%"
- **标题**: "系统可用性"
- **趋势**: "稳定运行"

#### 3.5 错误统计卡片
- **图标**: 警告图标
- **数值**: "2"
- **标题**: "今日错误"
- **趋势**: "-3 较昨日"

### 4. 功能模块区域
- **布局**: 3×2 网格
- **间距**: 32px
- **卡片尺寸**: 等宽，高度 200px

#### 功能卡片设计
- **背景**: 玻璃拟态效果
- **圆角**: 20px
- **内边距**: 32px
- **悬停效果**: 上升 + 阴影增强

#### 4.1 数据查询模块
- **图标**: 🔍 (64px)
- **标题**: "数据查询"
- **描述**: "查询用户信息、数据账户等"
- **按钮**: "立即查询"
- **颜色主题**: 蓝色

#### 4.2 版本管理模块
- **图标**: 📱 (64px)
- **标题**: "版本管理"
- **描述**: "管理应用版本、生成二维码"
- **按钮**: "版本列表"
- **颜色主题**: 绿色

#### 4.3 云影像工具模块
- **图标**: ☁️ (64px)
- **标题**: "云影像工具"
- **描述**: "生成各医院云影像链接"
- **按钮**: "生成链接"
- **颜色主题**: 紫色

#### 4.4 会诊管理模块
- **图标**: 👥 (64px)
- **标题**: "会诊管理"
- **描述**: "管理会诊记录和群组"
- **按钮**: "管理会诊"
- **颜色主题**: 橙色

#### 4.5 工具箱模块
- **图标**: 🛠️ (64px)
- **标题**: "工具箱"
- **描述**: "字符串处理、翻译等工具"
- **按钮**: "打开工具"
- **颜色主题**: 红色

#### 4.6 系统设置模块
- **图标**: ⚙️ (64px)
- **标题**: "系统设置"
- **描述**: "系统配置和参数管理"
- **按钮**: "系统设置"
- **颜色主题**: 灰色

### 5. 最近活动区域
- **高度**: 300px
- **背景**: 玻璃拟态效果
- **标题**: "最近活动"

#### 活动日志设计
- **布局**: 垂直列表
- **项目高度**: 48px
- **内容**: 时间 + 操作描述
- **样式**: 时间轴设计
- **滚动**: 最多显示8条记录

## 响应式设计

### 桌面端 (≥1200px)
- 统计卡片: 5列布局
- 功能模块: 3列布局
- 导航菜单: 完整显示

### 平板端 (768px - 1199px)
- 统计卡片: 3列布局，部分换行
- 功能模块: 2列布局
- 导航菜单: 部分收缩

### 移动端 (≤767px)
- 统计卡片: 2列布局
- 功能模块: 1列布局
- 导航菜单: 汉堡菜单
- 欢迎区域: 垂直布局

## 交互设计

### 1. 卡片交互
- **悬停**: 上升效果 + 阴影增强
- **点击**: 轻微下压 + 页面跳转
- **加载**: 骨架屏效果

### 2. 导航交互
- **菜单项悬停**: 背景高亮
- **当前页面**: 特殊标记
- **下拉菜单**: 平滑展开/收起

### 3. 数据更新
- **实时刷新**: 统计数据每30秒更新
- **活动日志**: 新活动淡入效果
- **状态指示**: 实时状态更新

## 动画效果

### 1. 页面加载
- **导航栏**: 从上方滑入 (0.5s)
- **欢迎区域**: 淡入 (0.6s)
- **统计卡片**: 依次从下方滑入 (0.8s)
- **功能模块**: 依次缩放进入 (1.0s)

### 2. 交互动画
- **卡片悬停**: 上升 + 阴影 (0.3s)
- **按钮点击**: 涟漪效果
- **数据更新**: 数字滚动动画

### 3. 状态变化
- **在线状态**: 脉动效果
- **错误提示**: 红色闪烁
- **成功操作**: 绿色确认动画
