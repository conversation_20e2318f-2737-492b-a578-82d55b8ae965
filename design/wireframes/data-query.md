# 数据查询页面线框图设计 - Annet Assistant

## 页面概述
数据查询页面是系统的核心功能之一，提供用户信息查询、数据账户查询等功能。采用左右分栏布局，左侧为查询表单，右侧为查询结果展示。

## 布局结构

### 整体布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              顶部导航栏                                      │
│  ┌─────────────┐  ┌─────────────────────────────────┐  ┌─────────────────┐  │
│  │    Logo     │  │           导航菜单              │  │   用户信息      │  │
│  └─────────────┘  └─────────────────────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│                                主内容区                                      │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                          页面标题区域                                │    │
│  │  ┌─────────────────┐                    ┌─────────────────────┐    │    │
│  │  │   页面标题      │                    │     面包屑导航      │    │    │
│  │  │   数据查询      │                    │  首页 > 数据查询    │    │    │
│  │  └─────────────────┘                    └─────────────────────┘    │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        查询选项卡区域                                │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                 │    │
│  │  │ 用户信息查询│  │数据账户查询 │  │ 关联信息查询│                 │    │
│  │  └─────────────┘  └─────────────┘  └─────────────┘                 │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        主查询区域                                    │    │
│  │  ┌─────────────────────────┐    ┌─────────────────────────────────┐ │    │
│  │  │       查询表单          │    │         查询结果                │ │    │
│  │  │                         │    │                                 │ │    │
│  │  │  ┌─────────────────┐   │    │  ┌─────────────────────────┐   │ │    │
│  │  │  │   查询类型      │   │    │  │       结果标题          │   │ │    │
│  │  │  └─────────────────┘   │    │  └─────────────────────────┘   │ │    │
│  │  │                         │    │                                 │ │    │
│  │  │  ┌─────────────────┐   │    │  ┌─────────────────────────┐   │ │    │
│  │  │  │   手机号输入    │   │    │  │       用户基础信息      │   │ │    │
│  │  │  └─────────────────┘   │    │  │  姓名: 张三             │   │ │    │
│  │  │                         │    │  │  手机: 138****1234     │   │ │    │
│  │  │  ┌─────────────────┐   │    │  │  角色: 医生             │   │ │    │
│  │  │  │   查询按钮      │   │    │  │  状态: 正常             │   │ │    │
│  │  │  └─────────────────┘   │    │  └─────────────────────────┘   │ │    │
│  │  │                         │    │                                 │ │    │
│  │  │  ┌─────────────────┐   │    │  ┌─────────────────────────┐   │ │    │
│  │  │  │   清空按钮      │   │    │  │       账户信息          │   │ │    │
│  │  │  └─────────────────┘   │    │  │  数据账户: zhangsan     │   │ │    │
│  │  │                         │    │  │  组织: 某某医院         │   │ │    │
│  │  │  ┌─────────────────┐   │    │  │  部门: 内科             │   │ │    │
│  │  │  │   查询历史      │   │    │  │  科室: 心内科           │   │ │    │
│  │  │  │  • 138****1234  │   │    │  └─────────────────────────┘   │ │    │
│  │  │  │  • 139****5678  │   │    │                                 │ │    │
│  │  │  │  • 136****9012  │   │    │  ┌─────────────────────────┐   │ │    │
│  │  │  └─────────────────┘   │    │  │       操作按钮          │   │ │    │
│  │  │                         │    │  │  [导出数据] [打印]      │   │ │    │
│  │  └─────────────────────────┘    │  └─────────────────────────┘   │ │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                        统计信息区域                                  │    │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐                │    │
│  │  │今日查询 │  │本周查询 │  │本月查询 │  │总查询数 │                │    │
│  │  │   89    │  │   456   │  │  1,234  │  │ 12,345 │                │    │
│  │  └─────────┘  └─────────┘  └─────────┘  └─────────┘                │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 详细组件设计

### 1. 页面标题区域
- **高度**: 80px
- **背景**: 玻璃拟态效果
- **布局**: 左右分栏

#### 1.1 页面标题 (左侧)
- **主标题**: "数据查询"
- **副标题**: "查询用户信息和数据账户"
- **字体**: 主标题 24px, 副标题 14px

#### 1.2 面包屑导航 (右侧)
- **路径**: "首页 > 数据查询"
- **样式**: 链接形式，可点击
- **分隔符**: ">" 符号

### 2. 查询选项卡区域
- **高度**: 60px
- **背景**: 玻璃拟态效果
- **布局**: 水平选项卡

#### 选项卡设计
- **用户信息查询**: 默认激活
- **数据账户查询**: 次要选项
- **关联信息查询**: 次要选项
- **样式**: 玻璃按钮效果，激活状态高亮

### 3. 主查询区域
- **布局**: 左右分栏 (40% : 60%)
- **间距**: 32px
- **高度**: 自适应内容

#### 3.1 查询表单 (左侧)
- **背景**: 玻璃拟态效果
- **圆角**: 20px
- **内边距**: 32px

##### 查询类型选择
- **组件**: 单选按钮组
- **选项**: 手机号查询、用户ID查询、姓名查询
- **默认**: 手机号查询

##### 输入框设计
- **手机号输入框**:
  - 占位符: "请输入手机号"
  - 验证: 实时格式验证
  - 样式: 玻璃输入框效果
  - 图标: 手机图标

##### 按钮组
- **查询按钮**:
  - 样式: 主要按钮 (蓝色渐变)
  - 尺寸: 100% 宽度
  - 状态: 加载状态显示

- **清空按钮**:
  - 样式: 次要按钮
  - 功能: 清空表单和结果

##### 查询历史
- **标题**: "最近查询"
- **列表**: 最多显示5条
- **交互**: 点击可快速查询
- **样式**: 小号文字，半透明背景

#### 3.2 查询结果 (右侧)
- **背景**: 玻璃拟态效果
- **圆角**: 20px
- **内边距**: 32px

##### 结果状态
- **无结果**: 显示提示信息
- **加载中**: 显示加载动画
- **有结果**: 显示详细信息
- **错误**: 显示错误提示

##### 用户基础信息卡片
- **标题**: "用户基础信息"
- **字段**:
  - 姓名
  - 手机号 (脱敏显示)
  - 用户角色
  - 账户状态
  - 注册时间
  - 最后登录

##### 数据账户信息卡片
- **标题**: "数据账户信息"
- **字段**:
  - 数据账户
  - 所属组织
  - 部门信息
  - 科室信息
  - 创建时间
  - 更新时间

##### 操作按钮区域
- **导出数据**: 导出为Excel
- **打印结果**: 打印查询结果
- **复制信息**: 复制到剪贴板
- **查看详情**: 跳转详情页面

### 4. 统计信息区域
- **高度**: 120px
- **背景**: 玻璃拟态效果
- **布局**: 4列网格

#### 统计卡片设计
- **今日查询**: 当日查询次数
- **本周查询**: 本周查询次数
- **本月查询**: 本月查询次数
- **总查询数**: 历史总查询次数

每个卡片包含:
- 数字 (大字体)
- 标签 (小字体)
- 趋势图标 (可选)

## 交互设计

### 1. 表单交互
- **输入验证**: 实时验证手机号格式
- **自动完成**: 基于历史查询的自动完成
- **快捷操作**: 支持回车键查询

### 2. 结果展示
- **渐进加载**: 结果分步显示
- **展开收起**: 详细信息可展开收起
- **复制功能**: 一键复制关键信息

### 3. 历史记录
- **自动保存**: 自动保存查询历史
- **快速查询**: 点击历史记录快速查询
- **清除历史**: 支持清除历史记录

## 响应式设计

### 桌面端 (≥1024px)
- 左右分栏布局 (40% : 60%)
- 统计卡片 4列显示
- 完整功能展示

### 平板端 (768px - 1023px)
- 左右分栏布局 (45% : 55%)
- 统计卡片 2×2 布局
- 部分功能收缩

### 移动端 (≤767px)
- 上下布局，表单在上
- 统计卡片 2×2 布局
- 简化操作按钮

## 数据可视化

### 1. 查询趋势图
- **类型**: 折线图
- **数据**: 最近7天查询量
- **位置**: 统计区域下方

### 2. 用户分布图
- **类型**: 饼图
- **数据**: 用户角色分布
- **交互**: 点击查看详情

### 3. 状态统计
- **类型**: 柱状图
- **数据**: 用户状态分布
- **颜色**: 状态对应色彩

## 性能优化

### 1. 查询优化
- **防抖处理**: 输入防抖，减少请求
- **缓存机制**: 查询结果缓存
- **分页加载**: 大量结果分页显示

### 2. 界面优化
- **骨架屏**: 加载时显示骨架屏
- **虚拟滚动**: 长列表虚拟滚动
- **懒加载**: 图片和组件懒加载

## 无障碍设计

### 1. 键盘导航
- **Tab 顺序**: 逻辑的Tab导航顺序
- **快捷键**: 支持常用快捷键
- **焦点指示**: 清晰的焦点指示

### 2. 屏幕阅读器
- **语义标签**: 使用语义化HTML
- **ARIA 属性**: 适当的ARIA属性
- **状态通知**: 状态变化通知

### 3. 视觉辅助
- **高对比度**: 支持高对比度模式
- **字体缩放**: 支持字体大小调整
- **色彩辅助**: 不仅依赖颜色传达信息
