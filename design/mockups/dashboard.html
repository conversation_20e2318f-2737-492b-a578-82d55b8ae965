<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Annet Assistant - 仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1f2937;
        }

        /* 背景装饰 */
        .bg-decoration {
            position: fixed;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
            pointer-events: none;
        }

        .bg-shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .bg-shape:nth-child(1) {
            width: 300px;
            height: 300px;
            top: 5%;
            left: 5%;
            animation-delay: 0s;
        }

        .bg-shape:nth-child(2) {
            width: 200px;
            height: 200px;
            top: 50%;
            right: 10%;
            animation-delay: 3s;
        }

        .bg-shape:nth-child(3) {
            width: 150px;
            height: 150px;
            bottom: 10%;
            left: 15%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }

        /* 主容器 */
        .main-container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 32px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
        }

        .nav-item {
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            color: white;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .user-name {
            font-weight: 500;
            margin-right: 8px;
        }

        .dropdown-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        /* 主内容区 */
        .main-content {
            padding: 32px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 欢迎区域 */
        .welcome-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideInDown 0.8s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-info h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .welcome-info p {
            font-size: 16px;
            color: #6b7280;
        }

        .system-status {
            text-align: right;
        }

        .status-item {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-indicator.online {
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 统计卡片区域 */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 18px;
            padding: 28px;
            text-align: center;
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }
        .stat-card:nth-child(5) { animation-delay: 0.5s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.3);
        }

        .stat-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .stat-trend {
            font-size: 14px;
            color: #10b981;
            font-weight: 500;
        }

        .stat-trend.negative {
            color: #ef4444;
        }
        /* 功能模块区域 */
        .modules-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 32px;
            margin-bottom: 40px;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            animation: scaleIn 0.6s ease-out;
            animation-fill-mode: both;
        }

        .module-card:nth-child(1) { animation-delay: 0.1s; }
        .module-card:nth-child(2) { animation-delay: 0.2s; }
        .module-card:nth-child(3) { animation-delay: 0.3s; }
        .module-card:nth-child(4) { animation-delay: 0.4s; }
        .module-card:nth-child(5) { animation-delay: 0.5s; }
        .module-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .module-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 16px 48px rgba(31, 38, 135, 0.4);
        }

        .module-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .module-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .module-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .module-btn {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(59, 130, 246, 0.6));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(37, 99, 235, 0.3);
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .module-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        /* 最近活动区域 */
        .activity-section {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 20px;
            padding: 32px;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .activity-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .activity-list {
            list-style: none;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding-left: 12px;
            padding-right: 12px;
        }

        .activity-time {
            font-size: 12px;
            color: #9ca3af;
            min-width: 120px;
        }

        .activity-content {
            flex: 1;
            font-size: 14px;
            color: #4b5563;
            margin-left: 16px;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            background: rgba(37, 99, 235, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-left: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-content {
                padding: 24px;
            }

            .navbar {
                padding: 0 24px;
            }

            .welcome-section {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .system-status {
                text-align: center;
            }

            .stats-section {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
            }

            .modules-section {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 24px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 16px;
            }

            .nav-menu {
                display: none;
            }

            .main-content {
                padding: 16px;
            }

            .welcome-section {
                padding: 24px;
            }

            .welcome-info h1 {
                font-size: 24px;
            }

            .stats-section {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-icon {
                font-size: 36px;
            }

            .stat-number {
                font-size: 24px;
            }

            .modules-section {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .module-card {
                padding: 24px;
            }

            .module-icon {
                font-size: 48px;
            }
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration">
        <div class="bg-shape"></div>
        <div class="bg-shape"></div>
        <div class="bg-shape"></div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="logo">
                <div class="logo-icon">🏥</div>
                <span>Annet Assistant</span>
            </div>

            <div class="nav-menu">
                <a href="#" class="nav-item active">首页</a>
                <a href="#" class="nav-item">数据查询</a>
                <a href="#" class="nav-item">版本管理</a>
                <a href="#" class="nav-item">云影像</a>
                <a href="#" class="nav-item">会诊管理</a>
                <a href="#" class="nav-item">工具箱</a>
            </div>

            <div class="user-info">
                <div class="user-avatar">👤</div>
                <span class="user-name">管理员</span>
                <span class="dropdown-arrow">▼</span>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 欢迎区域 -->
            <section class="welcome-section">
                <div class="welcome-info">
                    <h1>欢迎回来，管理员</h1>
                    <p>今天是 2025年1月15日，星期三</p>
                </div>
                <div class="system-status">
                    <div class="status-item">
                        服务状态：正常运行
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="status-item">
                        在线用户：128 人
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="status-item">
                        系统负载：正常
                        <div class="status-indicator online"></div>
                    </div>
                </div>
            </section>

            <!-- 统计卡片区域 -->
            <section class="stats-section">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">注册用户</div>
                    <div class="stat-trend">+12 本月</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">📱</div>
                    <div class="stat-number">56</div>
                    <div class="stat-label">应用版本</div>
                    <div class="stat-trend">+3 本周</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔍</div>
                    <div class="stat-number">89</div>
                    <div class="stat-label">今日查询</div>
                    <div class="stat-trend">+15 较昨日</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🖥️</div>
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                    <div class="stat-trend">稳定运行</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-number">2</div>
                    <div class="stat-label">今日错误</div>
                    <div class="stat-trend negative">-3 较昨日</div>
                </div>
            </section>

            <!-- 功能模块区域 -->
            <section class="modules-section">
                <div class="module-card" onclick="navigateTo('data-query')">
                    <div class="module-icon">🔍</div>
                    <h3 class="module-title">数据查询</h3>
                    <p class="module-description">查询用户信息、数据账户等相关数据</p>
                    <a href="#" class="module-btn">立即查询</a>
                </div>

                <div class="module-card" onclick="navigateTo('version-management')">
                    <div class="module-icon">📱</div>
                    <h3 class="module-title">版本管理</h3>
                    <p class="module-description">管理应用版本、生成下载二维码</p>
                    <a href="#" class="module-btn">版本列表</a>
                </div>

                <div class="module-card" onclick="navigateTo('cloud-image')">
                    <div class="module-icon">☁️</div>
                    <h3 class="module-title">云影像工具</h3>
                    <p class="module-description">生成各医院云影像查看链接</p>
                    <a href="#" class="module-btn">生成链接</a>
                </div>

                <div class="module-card" onclick="navigateTo('consultation')">
                    <div class="module-icon">👥</div>
                    <h3 class="module-title">会诊管理</h3>
                    <p class="module-description">管理会诊记录和腾讯云群组</p>
                    <a href="#" class="module-btn">管理会诊</a>
                </div>

                <div class="module-card" onclick="navigateTo('toolbox')">
                    <div class="module-icon">🛠️</div>
                    <h3 class="module-title">工具箱</h3>
                    <p class="module-description">字符串处理、翻译等实用工具</p>
                    <a href="#" class="module-btn">打开工具</a>
                </div>

                <div class="module-card" onclick="navigateTo('settings')">
                    <div class="module-icon">⚙️</div>
                    <h3 class="module-title">系统设置</h3>
                    <p class="module-description">系统配置和参数管理</p>
                    <a href="#" class="module-btn">系统设置</a>
                </div>
            </section>

            <!-- 最近活动区域 -->
            <section class="activity-section">
                <h3 class="activity-title">最近活动</h3>
                <ul class="activity-list">
                    <li class="activity-item">
                        <span class="activity-time">14:30</span>
                        <span class="activity-content">用户查询: 138****1234</span>
                        <div class="activity-icon">🔍</div>
                    </li>
                    <li class="activity-item">
                        <span class="activity-time">14:25</span>
                        <span class="activity-content">版本发布: v2.1.0</span>
                        <div class="activity-icon">📱</div>
                    </li>
                    <li class="activity-item">
                        <span class="activity-time">14:20</span>
                        <span class="activity-content">云影像链接生成</span>
                        <div class="activity-icon">☁️</div>
                    </li>
                    <li class="activity-item">
                        <span class="activity-time">14:15</span>
                        <span class="activity-content">会诊记录清理</span>
                        <div class="activity-icon">👥</div>
                    </li>
                    <li class="activity-item">
                        <span class="activity-time">14:10</span>
                        <span class="activity-content">系统备份完成</span>
                        <div class="activity-icon">💾</div>
                    </li>
                </ul>
            </section>
        </main>
    </div>

    <script>
        // 导航功能
        function navigateTo(module) {
            console.log('导航到:', module);
            // 这里可以添加实际的导航逻辑
        }

        // 数字动画效果
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = parseInt(number.textContent.replace(/[^\d]/g, ''));
                if (isNaN(target)) return;

                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    if (number.textContent.includes('%')) {
                        number.textContent = current.toFixed(1) + '%';
                    } else {
                        number.textContent = Math.floor(current).toLocaleString();
                    }
                }, 30);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', () => {
            setTimeout(animateNumbers, 500);
        });

        // 用户下拉菜单
        document.querySelector('.user-info').addEventListener('click', function() {
            const arrow = this.querySelector('.dropdown-arrow');
            arrow.style.transform = arrow.style.transform === 'rotate(180deg)' ? 'rotate(0deg)' : 'rotate(180deg)';
        });

        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const dateString = now.toLocaleDateString('zh-CN', options);
            const timeElement = document.querySelector('.welcome-info p');
            if (timeElement) {
                timeElement.textContent = `今天是 ${dateString}`;
            }
        }

        // 每分钟更新时间
        setInterval(updateTime, 60000);
        updateTime();
    </script>
</body>
</html>
